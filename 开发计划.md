# 写乎 Markdown 编辑器 · 开发计划
> 基于 `xiehu_full_blueprint.md` 蓝图文档制定的详细开发计划
> 创建时间：2025-07-28
> 项目周期：12 周
> 版本：v2.1

---

## 项目概述

### 核心定位
| 维度         | 描述 |
|--------------|------|
| **目标用户** | 技术写作者、开源维护者、公众号运营、小团队协作 |
| **核心卖点** | 离线可写 · 所见即所得 · Git 原生 · 微信富文本一键导出 |
| **差异化**   | 同时解决 *离线 + Git + 协作 + 公众号排版* 四件痛点 |

### 技术栈一览
| 层次           | 主要选型                                   | 说明 |
|----------------|--------------------------------------------|------|
| **前端框架**   | **Next.js 20.x (React 19 · App Router)**    | RSC + Edge SSR + PWA 插件 |
| 编辑器内核     | **CodeMirror 6**                           | Decoration API 支持 Hybrid |
| Markdown 渲染  | **markdown‑it** + `emoji/katex/mermaid`    | WebWorker 内解析 |
| 实时协作       | **Yjs** + `y-websocket` / PartyKit Relay   | CRDT 无冲突 |
| Git 工作流     | **isomorphic‑git** + `lightning-fs`        | 浏览器端 commit/push |
| 离线缓存       | **Workbox SW** + **IndexedDB (idb)**       | 页面+数据离线 |
| 样式/组件      | **Tailwind CSS** + **shadcn/ui**           | 暗黑 & 主题切换 |
| 安全审查       | **DOMPurify** + **ONNX Runtime Web**       | XSS & 违规本地检测 |
| 监控部署       | **Sentry** + Vercel / Cloudflare Pages     | CI/CD + Trace |

### 开发工具链
- **包管理**：npm Workspaces (monorepo 架构)
- **测试框架**：vitest (覆盖率 ≥ 80%)
- **代码质量**：ESLint + Prettier + lint-staged
- **CI/CD**：GitHub Actions + 自动化部署
- **性能监控**：WebVitals + PerfObserver

---

## 功能模块与验收标准

| 模块 | 目标 | 关键行为 | 技术要点 | 验收标准 |
|------|------|----------|----------|----------|
| **编辑核心** | 100k 字不卡 | Hybrid 行内 WYSIWYG；分栏写看；Vim/Emacs | CM6 Decoration + 虚拟滚动 | 光标耗时 ≤ 5ms；模式切换 ≤ 150ms |
| **预览排版** | 所见即所得一致 | KaTeX / Mermaid / PlantUML；粘贴图即上传 | Worker 渲染 + IO 懒加载 | Mermaid 200 行首帧 ≤ 800ms |
| **目录导航** | 快速定位长文 | 自动 H1–H3；折叠/搜索；滚动高亮 | CM6 syntaxTree；IO + RAF | 刷新 ≤ 2ms/千行；高亮 120fps |
| **实时协作** | 0 冲突、多端离线 | 光标 & 选区；@评论；离线合并 | Yjs + IndexedDB buffer | 延迟 ≤ 200ms（50 人） |
| **Git 集成** | 保存 = commit | OAuth；push/pull；版本 Diff | isomorphic‑git | clone 10MB ≤ 3s；push ≤ 1s |
| **离线同步** | 无网也能写 | SW 缓存；BG Sync push | Workbox；gitQueue | 断网刷新可写；重连 3s 内同步 |
| **公众号输出** | 一键富文本 | 代码复制按钮；1080px 压图 | HTML 白名单转换器 | 微信粘贴 100% 正常 |
| **插件生态** | VSCode 式扩展 | JSON 清单；命令/面板钩子 | ESModule + iframe | 插件热装卸不崩主线程 |

---

## UI 交互总览

```
┌──────── 顶部导航 56 px ──────────────────────────────────────────────┐
│ Logo │ 草稿 / Doc.md ●已保存 ─── 🗂️目录 │ 📂文件 │ 🔍搜索 │ 发布 │ 👤 │
└────────────────────────────────────────────────────────────────────┘
┌─ 主导航 48 px ─┬─ 功能抽屉 240 px (目录默认) ─┬──── 编辑区 ───┬─ 预览 30% ─┐
│ 📝 片段        │ H1–H3 树 · 折叠/搜索        │ Markdown 源   │ Rendered   │
│ 📂 文件        │ 文件树 / 历史              │ (Hybrid 可切) │ HTML       │
│ ⏳ 版本        │ …                          │               │            │
└────────────────┴────────────────────────────┴────────────────┴───────────┘
                                 ⤵︎ Status Dock：字数 · 分支 · 主题
```
*768–1279 px*：抽屉滑出、预览改 Tab； *≤ 767 px*：单栏编辑 + 底部 ActionSheet。

---

## 12 周开发排期

### 第一阶段：MVP 基础 (第 1-2 周)
**目标**：建立核心编辑功能和基础架构

#### 1.1 项目初始化
- [ ] 创建 monorepo 结构 (npm workspaces)
- [ ] 配置 Next.js 20.x + App Router + RSC
- [ ] 设置 TypeScript 严格模式 + ESLint + Prettier
- [ ] 配置 Tailwind CSS + shadcn/ui + 暗黑主题
- [ ] 建立基础 CI/CD 流程 (GitHub Actions)

#### 1.2 编辑器核心 (CodeMirror 6)
- [ ] 集成 CM6 基础配置 + Markdown 语法高亮
- [ ] 开发 Hybrid 编辑模式 (Decoration API)
- [ ] 实现段落缓存 Map + 节流 100ms
- [ ] 添加 Vim/Emacs 键位支持
- [ ] 虚拟滚动支持 (100k 字不卡)

#### 1.3 预览系统
- [ ] 集成 markdown-it + emoji/katex/mermaid 插件
- [ ] WebWorker 内解析 + IO 懒加载
- [ ] 实现分栏预览布局 (编辑 70% + 预览 30%)
- [ ] 添加实时预览同步 (延迟 < 150ms)
- [ ] 响应式布局适配 (移动端 Tab 切换)

#### 1.4 本地存储 (IndexedDB)
- [ ] 设计文档数据结构 + 版本管理
- [ ] 实现文档的本地保存/加载/删除
- [ ] 建立文件管理系统 (文件树)
- [ ] 添加自动保存功能 (3s 间隔)

**验收标准**：
- 编辑器可正常输入和渲染 Markdown
- 预览实时同步，延迟 < 150ms
- 支持 100k 字文档不卡顿 (光标耗时 ≤ 5ms)
- 本地数据持久化正常

### 第二阶段：PWA 离线 (第 3-4 周)
**目标**：实现完整的离线编辑体验

#### 2.1 Service Worker (Workbox)
- [ ] 配置 Workbox 预缓存策略
- [ ] 实现页面资源离线缓存 (静态资源)
- [ ] 添加 NetworkFirst 策略处理 .md 文件
- [ ] 建立后台同步机制 (Background Sync)
- [ ] 离线页面降级处理

#### 2.2 PWA 功能
- [ ] 配置 Web App Manifest (图标、主题色)
- [ ] 实现安装提示 (beforeinstallprompt)
- [ ] 添加离线状态检测 + UI 提示
- [ ] 优化离线 UI 体验 (骨架屏)
- [ ] 支持桌面/移动端安装

#### 2.3 性能优化
- [ ] 实现虚拟滚动 (长文档优化)
- [ ] 添加 Web Worker 渲染 (Mermaid/KaTeX)
- [ ] 优化首屏加载时间 (LCP < 1s)
- [ ] 实现懒加载策略 (图片/组件)
- [ ] 代码分割 + 动态导入

#### 2.4 目录导航系统
- [ ] 使用 CM6 syntaxTree 抽取 H1–H3
- [ ] 实现目录树折叠/展开功能
- [ ] 添加目录搜索过滤
- [ ] IO 高亮 + 滚动同步 (120fps)
- [ ] 快速跳转功能

**验收标准**：
- 离线状态下可正常编辑
- PWA 安装成功率 > 95%
- 首屏加载时间 < 1s (LCP)
- 断网重连后 3s 内恢复同步
- 目录刷新 ≤ 2ms/千行

### 第三阶段：Git 集成 (第 5-6 周)
**目标**：实现完整的 Git 工作流

#### 3.1 Git 基础 (isomorphic-git)
- [ ] 集成 isomorphic-git + lightning-fs
- [ ] 实现 Git 仓库初始化 (git init)
- [ ] 支持基础的 commit/push/pull 操作
- [ ] 添加分支管理功能 (checkout/merge)
- [ ] 实现 Git 状态检查 (status/diff)

#### 3.2 OAuth 认证
- [ ] 集成 GitHub OAuth 2.0
- [ ] 支持 Gitea/GitLab 认证
- [ ] 实现 Token 安全存储 (加密)
- [ ] 添加多账户管理
- [ ] 支持 SSH Key 认证 (可选)

#### 3.3 版本控制 UI
- [ ] 设计版本历史界面 (commit log)
- [ ] 实现 Diff 可视化 (行级对比)
- [ ] 添加冲突解决界面
- [ ] 支持 Cherry-pick/Revert 操作
- [ ] 分支切换 + 合并 UI

#### 3.4 自动化工作流
- [ ] 实现保存即提交机制 (auto-commit)
- [ ] 添加 Git 队列管理 (localStorage.gitQueue)
- [ ] 支持批量推送 (SW sync 事件)
- [ ] 建立冲突检测机制
- [ ] 离线 Git 操作缓存

**验收标准**：
- Git 操作成功率 > 99%
- 10MB 仓库克隆时间 < 3s
- 推送延迟 < 1s
- 冲突解决界面友好
- 离线提交队列可靠

### 第四阶段：微信导出 (第 7-8 周)
**目标**：实现公众号富文本一键导出

#### 4.1 富文本转换
- [ ] 开发 Markdown 到微信 HTML 转换器
- [ ] 实现代码块样式适配 (语法高亮)
- [ ] 支持图片压缩 (1080px 最佳)
- [ ] 添加样式白名单过滤 (DOMPurify)
- [ ] 处理特殊字符转义

#### 4.2 导出功能
- [ ] 实现一键复制富文本 (Clipboard API)
- [ ] 添加代码复制按钮 (每个代码块)
- [ ] 支持自定义主题样式 (多套模板)
- [ ] 实现预览模式 (微信样式预览)
- [ ] 导出 PDF/图片格式 (可选)

#### 4.3 图片处理
- [ ] 集成图片上传功能 (拖拽/粘贴)
- [ ] 实现拖拽粘贴上传
- [ ] 添加图片压缩优化 (WebP/JPEG)
- [ ] 支持 CDN 图片链接 (七牛/阿里云)
- [ ] 图片懒加载 + 占位符

#### 4.4 样式系统
- [ ] 内置多套公众号主题
- [ ] 支持自定义 CSS 样式
- [ ] 代码块主题切换 (GitHub/VS Code)
- [ ] 响应式样式适配
- [ ] 暗黑模式支持

**验收标准**：
- 微信粘贴成功率 100%
- 图片压缩质量保持良好
- 代码块样式完美适配
- 导出速度 < 2s
- 支持 5+ 主题样式

### 第五阶段：实时协作 (第 9-10 周)
**目标**：实现多人实时协作编辑

#### 5.1 CRDT 集成 (Yjs)
- [ ] 集成 Yjs CRDT 引擎
- [ ] 实现文档状态同步 (Y.Doc)
- [ ] 添加冲突自动解决 (OT 算法)
- [ ] 支持离线协作合并
- [ ] 建立状态持久化 (IndexedDB)

#### 5.2 实时功能
- [ ] 实现光标位置同步 (多色标识)
- [ ] 添加选区共享显示 (半透明高亮)
- [ ] 支持 @评论功能 (侧边栏)
- [ ] 实现在线用户列表 (头像显示)
- [ ] 添加用户操作提示 (正在输入...)

#### 5.3 协作服务
- [ ] 部署 WebSocket 中继服务
- [ ] 集成 PartyKit 或自建服务
- [ ] 添加房间管理功能 (创建/加入)
- [ ] 实现权限控制 (只读/编辑/管理)
- [ ] 建立用户认证系统

#### 5.4 离线协作
- [ ] 离线编辑缓存机制
- [ ] 重连后自动合并策略
- [ ] 冲突提示和解决 UI
- [ ] 版本分叉处理
- [ ] 数据一致性保证

**验收标准**：
- 50 人同时协作延迟 < 200ms
- 离线协作合并无冲突
- 光标同步准确率 > 99%
- 评论功能完整可用
- 权限控制有效

### 第六阶段：插件生态 & Beta (第 11-12 周)
**目标**：建立可扩展的插件系统

#### 6.1 插件架构
- [ ] 设计插件 API 规范 (TypeScript 定义)
- [ ] 实现 ESModule 插件加载器
- [ ] 添加 iframe 沙箱隔离机制
- [ ] 支持插件热装卸 (不重启)
- [ ] 建立插件生命周期管理

#### 6.2 核心插件
- [ ] 开发 AI 写作助手插件 (GPT 集成)
- [ ] 实现内容审核插件 (ONNX Runtime)
- [ ] 添加图表生成插件 (Chart.js/D3)
- [ ] 支持模板管理插件 (文档模板)
- [ ] 开发代码执行插件 (可选)

#### 6.3 插件市场
- [ ] 建立插件注册机制 (JSON 清单)
- [ ] 实现插件商店界面
- [ ] 添加插件评分系统
- [ ] 支持插件更新机制
- [ ] 建立插件安全审核

#### 6.4 完善功能
- [ ] 全局搜索功能 (文档内容)
- [ ] 快捷键系统 (可自定义)
- [ ] 主题切换 (亮/暗/自定义)
- [ ] 多语言支持 (i18n)
- [ ] 无障碍访问 (a11y)

#### 6.5 测试与发布
- [ ] 单元测试完善 (vitest)
- [ ] E2E 测试 (Playwright)
- [ ] 性能基准测试
- [ ] 用户体验测试
- [ ] Beta 版本发布

**验收标准**：
- 插件系统稳定不崩溃
- 插件加载时间 < 500ms
- API 文档完整清晰
- 至少 5 个可用插件
- Beta 版本可用性 > 95%

---

## 核心实现要点

### Hybrid 编辑技术
- **Decoration API**：使用 CodeMirror 6 的 Decoration.widget 覆盖非光标段落
- **段落缓存**：建立 Map 缓存机制，节流 100ms 更新
- **虚拟滚动**：支持 100k+ 字文档流畅编辑
- **模式切换**：源码/预览/混合模式无缝切换

### Git 队列机制
- **离线队列**：commit 保存到 `localStorage.gitQueue`
- **后台同步**：Service Worker `sync` 事件批量 `git.push`
- **冲突处理**：三方合并算法 + 用户手动解决
- **状态管理**：Git 操作状态实时反馈

### PWA 离线策略
- **缓存策略**：Workbox 预缓存 + `NetworkFirst` for `.md`
- **安装提示**：`beforeinstallprompt` 智能安装引导
- **离线编辑**：完全离线可用，在线后 3s 内自动同步
- **数据同步**：IndexedDB + Background Sync 保证数据一致性

### 安全与合规
- **XSS 防护**：DOMPurify 清洗用户输入
- **CSP 策略**：Content Security Policy + Nonce
- **iframe 沙箱**：插件域名白名单 + 权限控制
- **本地审核**：ONNX Runtime Web 违规图文检测
- **企业版**：敏感词库 + 审计 Webhook

---

## 性能指标与监控

| 指标类别 | 具体指标 | 目标值 | 监控工具 |
|----------|----------|--------|----------|
| **核心性能** | LCP (最大内容绘制) | ≤ 1s | WebVitals |
| | 输入延迟 | ≤ 50ms | PerfObserver |
| | 光标响应时间 | ≤ 5ms | 自定义埋点 |
| | 模式切换时间 | ≤ 150ms | Performance API |
| **编辑性能** | 100k 字文档加载 | ≤ 2s | 性能测试 |
| | Mermaid 200 行渲染 | ≤ 800ms | Worker 监控 |
| | 目录刷新速度 | ≤ 2ms/千行 | RAF 计时 |
| **协作性能** | 50 人协作延迟 | ≤ 200ms | WebSocket 监控 |
| | Git 操作成功率 | ≥ 99% | 错误统计 |
| | PWA 离线覆盖率 | ≥ 95% | Workbox 统计 |
| **稳定性** | JS 错误率 | ≤ 0.1% | Sentry |
| | 崩溃率 | ≤ 0.01% | 错误边界 |

---

## 项目结构

```
xiehu-editor/
├── apps/
│   └── web/                 # Next.js 主应用
│       ├── app/            # App Router 页面
│       ├── components/     # React 组件
│       ├── lib/           # 工具函数
│       └── public/        # 静态资源
├── packages/
│   ├── editor-core/       # 编辑器核心
│   ├── renderer/          # Markdown 渲染器
│   ├── outline/           # 目录导航
│   ├── git-integration/   # Git 集成
│   ├── collaboration/     # 实时协作
│   ├── export/           # 导出功能
│   └── ui/               # UI 组件库
├── scripts/              # 构建脚本
├── docs/                # 项目文档
└── tests/               # 测试文件
```

## 开发规范

### 代码规范
- 使用 TypeScript 严格模式
- 遵循 ESLint + Prettier 配置
- 组件使用 React 19 新特性
- 优先使用 App Router

### Git 规范
- 分支命名：`feature/功能名`、`fix/问题描述`
- 提交格式：`feat(scope): description #issue`
- 使用 squash merge
- 每个 PR 必须通过 CI 检查

### 测试规范
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖核心流程
- E2E 测试覆盖用户关键路径
- 性能测试确保指标达标

## 风险评估与应对

### 技术风险
1. **CodeMirror 6 性能**：大文档编辑可能卡顿
   - 应对：虚拟滚动 + 分段渲染
2. **isomorphic-git 兼容性**：浏览器 Git 操作限制
   - 应对：降级方案 + 服务端代理
3. **实时协作复杂度**：CRDT 冲突处理
   - 应对：简化协作场景 + 手动解决

### 进度风险
1. **技术学习曲线**：新技术栈学习时间
   - 应对：提前技术调研 + 原型验证
2. **功能复杂度**：功能间耦合度高
   - 应对：模块化设计 + 渐进开发
3. **第三方依赖**：关键库版本兼容性
   - 应对：版本锁定 + 降级方案
4. **浏览器兼容**：Safari/Firefox 特殊处理
   - 应对：Polyfill + 功能检测

---

## 质量保证体系

### 测试策略
- **单元测试**：vitest + @testing-library (覆盖率 ≥ 80%)
- **集成测试**：关键业务流程端到端验证
- **E2E 测试**：Playwright 自动化测试
- **性能测试**：Lighthouse CI + 自定义性能基准
- **兼容性测试**：BrowserStack 多浏览器验证
- **安全测试**：OWASP 安全扫描 + 渗透测试

### CI/CD 流程
```mermaid
graph LR
    A[代码提交] --> B[ESLint/Prettier]
    B --> C[单元测试]
    C --> D[构建打包]
    D --> E[E2E 测试]
    E --> F[性能测试]
    F --> G[安全扫描]
    G --> H[部署预览]
    H --> I[人工验收]
    I --> J[生产部署]
```

### 发布策略
- **Alpha 版本**：内部测试 (第 8 周)
- **Beta 版本**：公开测试 (第 12 周)
- **RC 版本**：候选发布 (第 14 周)
- **正式版本**：稳定发布 (第 16 周)

---

## 团队协作

### 开发分工建议
- **前端架构师**：整体架构设计 + 核心模块开发
- **编辑器专家**：CodeMirror 6 深度定制 + 性能优化
- **Git 工程师**：isomorphic-git 集成 + 版本控制
- **协作专家**：Yjs CRDT + 实时同步功能
- **UI/UX 设计师**：界面设计 + 用户体验优化
- **测试工程师**：自动化测试 + 质量保证

### 沟通机制
- **每日站会**：进度同步 + 问题讨论
- **周度回顾**：里程碑检查 + 计划调整
- **技术分享**：新技术学习 + 最佳实践
- **代码评审**：所有 PR 必须 Review

---

## 下一步行动

### 立即执行 (第 1 周)
1. **项目初始化**：创建 GitHub 仓库 + 基础配置
2. **技术调研**：CodeMirror 6 Hybrid 模式 POC
3. **环境搭建**：开发/测试/部署环境准备
4. **团队组建**：确定开发团队和分工

### 短期目标 (第 1-4 周)
1. **MVP 完成**：基础编辑 + 预览功能
2. **PWA 集成**：离线功能 + 安装体验
3. **性能优化**：大文档编辑流畅度
4. **用户测试**：收集早期反馈

### 中期目标 (第 5-8 周)
1. **Git 集成**：完整版本控制工作流
2. **微信导出**：公众号富文本完美适配
3. **Alpha 测试**：内部版本验证
4. **文档完善**：开发者文档 + 用户手册

### 长期目标 (第 9-12 周)
1. **实时协作**：多人编辑功能
2. **插件生态**：可扩展架构
3. **Beta 发布**：公开测试版本
4. **社区建设**：用户反馈 + 持续改进

---

*本开发计划基于 xiehu_full_blueprint.md 制定，将根据实际开发进度和用户反馈进行动态调整*
