#!/usr/bin/env node

/**
 * 开发启动脚本
 * 启动开发服务器并打开浏览器
 */

const { spawn } = require('child_process')
const { platform } = require('os')

console.log('🚀 启动写乎 Markdown 编辑器开发服务器...\n')

// 启动开发服务器
const dev = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true
})

// 延迟打开浏览器
setTimeout(() => {
  const url = 'http://localhost:3000'
  console.log(`\n🌐 正在打开浏览器: ${url}`)
  
  let command
  switch (platform()) {
    case 'darwin':
      command = 'open'
      break
    case 'win32':
      command = 'start'
      break
    default:
      command = 'xdg-open'
  }
  
  spawn(command, [url], { stdio: 'ignore' })
}, 3000)

// 处理退出
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭开发服务器...')
  dev.kill('SIGINT')
  process.exit(0)
})

dev.on('close', (code) => {
  console.log(`\n开发服务器已关闭，退出码: ${code}`)
  process.exit(code)
})
