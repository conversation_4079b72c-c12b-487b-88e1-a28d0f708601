#!/usr/bin/env node

/**
 * 功能测试脚本
 * 自动化验证第一阶段核心功能
 */

const { execSync, spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🧪 写乎编辑器功能测试\n')

// 测试配置
const TEST_CONFIG = {
  port: 3000,
  timeout: 30000,
  testDataDir: path.join(__dirname, '../test-data')
}

// 创建测试数据目录
if (!fs.existsSync(TEST_CONFIG.testDataDir)) {
  fs.mkdirSync(TEST_CONFIG.testDataDir, { recursive: true })
}

// 生成测试用的 Markdown 内容
function generateTestMarkdown() {
  return `# 功能测试文档

## 基础语法测试

这是一段包含**粗体**、*斜体*和\`行内代码\`的文本。

### 列表测试
- 无序列表项 1
- 无序列表项 2
  - 嵌套项 A
  - 嵌套项 B

1. 有序列表项 1
2. 有序列表项 2

### 引用测试
> 这是一段引用文本
> 可以包含多行内容

### 代码块测试
\`\`\`javascript
function hello() {
  console.log("Hello World!");
  return "测试成功";
}
\`\`\`

### 数学公式测试
行内公式：$E = mc^2$

块级公式：
$$\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\cdots + x_n$$

### 图表测试
\`\`\`mermaid
graph TD
    A[开始] --> B{判断}
    B -->|是| C[执行]
    B -->|否| D[结束]
    C --> D
\`\`\`

### 链接和图片
[测试链接](https://example.com)
![测试图片](https://via.placeholder.com/150x100)

---

## 性能测试内容

${Array(100).fill(0).map((_, i) => `
### 章节 ${i + 1}
这是第 ${i + 1} 个测试章节，包含一些内容来测试编辑器的性能表现。

- 列表项 1
- 列表项 2
- 列表项 3

\`\`\`javascript
// 代码块 ${i + 1}
function test${i + 1}() {
  return "性能测试 ${i + 1}";
}
\`\`\`
`).join('\n')}

## 测试完成

如果您能看到这段文本，说明编辑器可以正常处理大型文档。
`
}

// 检查端口是否可用
function isPortAvailable(port) {
  try {
    execSync(`lsof -ti:${port}`, { stdio: 'pipe' })
    return false
  } catch {
    return true
  }
}

// 等待服务器启动
function waitForServer(port, timeout = 30000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    
    function check() {
      try {
        execSync(`curl -s http://localhost:${port} > /dev/null`, { stdio: 'pipe' })
        resolve()
      } catch {
        if (Date.now() - startTime > timeout) {
          reject(new Error('服务器启动超时'))
        } else {
          setTimeout(check, 1000)
        }
      }
    }
    
    check()
  })
}

// 主测试流程
async function runTests() {
  console.log('📋 开始功能测试...\n')
  
  // 1. 环境检查
  console.log('🔍 检查测试环境...')
  
  // 检查 Node.js 版本
  const nodeVersion = process.version
  console.log(`Node.js 版本: ${nodeVersion}`)
  
  if (parseInt(nodeVersion.slice(1)) < 18) {
    console.error('❌ Node.js 版本过低，需要 >= 18.0.0')
    process.exit(1)
  }
  
  // 检查依赖
  try {
    execSync('npm list --depth=0', { stdio: 'pipe' })
    console.log('✅ 依赖检查通过')
  } catch {
    console.log('⚠️  依赖不完整，正在安装...')
    execSync('npm install', { stdio: 'inherit' })
  }
  
  // 2. 构建测试
  console.log('\n🏗️  测试项目构建...')
  try {
    execSync('npm run build', { stdio: 'pipe' })
    console.log('✅ 构建测试通过')
  } catch (error) {
    console.error('❌ 构建失败')
    console.error(error.stdout?.toString() || error.message)
    process.exit(1)
  }
  
  // 3. 生成测试数据
  console.log('\n📝 生成测试数据...')
  const testMarkdown = generateTestMarkdown()
  const testFile = path.join(TEST_CONFIG.testDataDir, 'test-document.md')
  fs.writeFileSync(testFile, testMarkdown)
  console.log(`✅ 测试数据已生成: ${testFile}`)
  
  // 4. 启动开发服务器
  console.log('\n🚀 启动开发服务器...')
  
  if (!isPortAvailable(TEST_CONFIG.port)) {
    console.log(`⚠️  端口 ${TEST_CONFIG.port} 已被占用，尝试使用现有服务`)
  } else {
    console.log('启动新的开发服务器...')
    const devServer = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      detached: true
    })
    
    // 等待服务器启动
    try {
      await waitForServer(TEST_CONFIG.port, TEST_CONFIG.timeout)
      console.log('✅ 开发服务器启动成功')
    } catch (error) {
      console.error('❌ 开发服务器启动失败:', error.message)
      devServer.kill()
      process.exit(1)
    }
  }
  
  // 5. 功能测试指导
  console.log('\n🧪 功能测试指导')
  console.log('================')
  console.log(`
📖 请按照以下步骤进行手动测试：

1. 打开浏览器访问: http://localhost:${TEST_CONFIG.port}

2. 基础功能测试：
   ✅ 编辑器正常加载（< 500ms）
   ✅ 可以正常输入文本
   ✅ Markdown 语法高亮正确

3. Hybrid 模式测试：
   ✅ 点击"混合"按钮切换模式
   ✅ 非光标行显示渲染效果
   ✅ 光标行保持源码模式

4. 预览功能测试：
   ✅ 实时预览同步（< 150ms）
   ✅ 数学公式正确渲染
   ✅ 代码块语法高亮

5. 本地存储测试：
   ✅ 文档自动保存（3秒）
   ✅ 刷新页面内容保留
   ✅ 文件管理功能正常

6. 性能测试：
   ✅ 复制测试文档内容到编辑器
   ✅ 验证大文档编辑流畅度
   ✅ 检查内存使用情况

7. 主题和响应式测试：
   ✅ 主题切换功能正常
   ✅ 响应式布局适配

测试数据文件: ${testFile}
`)
  
  console.log('📋 详细测试指导请查看: 开发进度报告-第一阶段.md')
  console.log('\n🎯 测试完成后，请填写测试报告并反馈结果。')
  
  // 6. 打开浏览器（可选）
  if (process.argv.includes('--open')) {
    console.log('\n🌐 正在打开浏览器...')
    const { platform } = require('os')
    let command
    switch (platform()) {
      case 'darwin': command = 'open'; break
      case 'win32': command = 'start'; break
      default: command = 'xdg-open'
    }
    
    try {
      execSync(`${command} http://localhost:${TEST_CONFIG.port}`)
    } catch {
      console.log('⚠️  无法自动打开浏览器，请手动访问 http://localhost:3000')
    }
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('\n❌ 测试过程中发生错误:', error.message)
  process.exit(1)
})

process.on('SIGINT', () => {
  console.log('\n👋 测试已中断')
  process.exit(0)
})

// 运行测试
runTests().catch((error) => {
  console.error('\n❌ 测试失败:', error.message)
  process.exit(1)
})
