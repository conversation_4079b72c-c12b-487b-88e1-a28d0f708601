#!/usr/bin/env node

/**
 * 测试构建脚本
 * 验证项目是否可以正常构建
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 开始测试项目构建...\n')

// 检查必要文件
const requiredFiles = [
  'package.json',
  'turbo.json',
  'apps/web/package.json',
  'packages/editor-core/package.json',
  'packages/renderer/package.json',
  'packages/ui/package.json'
]

console.log('📁 检查项目文件结构...')
for (const file of requiredFiles) {
  if (!fs.existsSync(file)) {
    console.error(`❌ 缺少必要文件: ${file}`)
    process.exit(1)
  }
  console.log(`✅ ${file}`)
}

console.log('\n📦 检查依赖安装...')
try {
  execSync('npm list --depth=0', { stdio: 'pipe' })
  console.log('✅ 依赖已安装')
} catch (error) {
  console.log('⚠️  依赖未完全安装，尝试安装...')
  try {
    execSync('npm install', { stdio: 'inherit' })
    console.log('✅ 依赖安装完成')
  } catch (installError) {
    console.error('❌ 依赖安装失败')
    process.exit(1)
  }
}

console.log('\n🔧 测试 TypeScript 编译...')
try {
  execSync('npm run type-check', { stdio: 'pipe' })
  console.log('✅ TypeScript 编译通过')
} catch (error) {
  console.error('❌ TypeScript 编译失败')
  console.error(error.stdout?.toString() || error.message)
  process.exit(1)
}

console.log('\n🎨 测试代码格式检查...')
try {
  execSync('npm run lint', { stdio: 'pipe' })
  console.log('✅ 代码格式检查通过')
} catch (error) {
  console.log('⚠️  代码格式检查有警告，但不影响构建')
}

console.log('\n🏗️  测试项目构建...')
try {
  execSync('npm run build', { stdio: 'inherit' })
  console.log('✅ 项目构建成功')
} catch (error) {
  console.error('❌ 项目构建失败')
  process.exit(1)
}

console.log('\n🎉 所有测试通过！项目可以正常构建和运行。')
console.log('\n📋 下一步：')
console.log('  1. 运行 npm run dev 启动开发服务器')
console.log('  2. 访问 http://localhost:3000 查看应用')
console.log('  3. 开始第二阶段开发工作')
