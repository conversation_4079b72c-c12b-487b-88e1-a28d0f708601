# 写乎 Markdown 编辑器

> Local-first × Git 工作流 × 所见即所得 × 公众号排版

一个面向技术写作者的现代化 Markdown 编辑器，支持离线编辑、Git 集成、实时协作和公众号一键导出。

## ✨ 特性

- 🚀 **Hybrid 编辑模式** - 所见即所得的 Markdown 编辑体验
- 💾 **Local-first** - 离线优先，数据本地存储
- 🔄 **Git 集成** - 原生 Git 工作流，保存即提交
- 👥 **实时协作** - 多人同时编辑，无冲突合并
- 📱 **PWA 支持** - 可安装的 Web 应用
- 🎨 **公众号导出** - 一键生成微信富文本
- 🌙 **主题切换** - 支持亮色/暗色主题

## 🏗️ 技术栈

- **前端框架**: Next.js 20.x (React 19 + App Router)
- **编辑器内核**: CodeMirror 6 + Decoration API
- **Markdown 渲染**: markdown-it + KaTeX + Mermaid
- **状态管理**: Zustand + IndexedDB
- **样式系统**: Tailwind CSS + shadcn/ui
- **构建工具**: Turbo + TypeScript

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
```

## 📁 项目结构

```
xiehu-editor/
├── apps/
│   └── web/                 # Next.js 主应用
├── packages/
│   ├── editor-core/         # 编辑器核心
│   ├── renderer/            # Markdown 渲染器
│   └── ui/                  # UI 组件库
├── scripts/                 # 构建脚本
└── docs/                    # 项目文档
```

## 🛠️ 开发

### 代码规范

项目使用 ESLint + Prettier 进行代码格式化：

```bash
npm run lint      # 检查代码规范
npm run format    # 格式化代码
```

### 类型检查

```bash
npm run type-check
```

### 测试

```bash
# 运行单元测试
npm run test

# 构建测试
npm run test:build

# 功能测试（手动）
npm run test:features

# 功能测试并打开浏览器
npm run test:features:open
```

## 📋 开发计划

- [x] **第一阶段** (第 1-2 周): MVP 基础功能
- [ ] **第二阶段** (第 3-4 周): PWA 离线支持
- [ ] **第三阶段** (第 5-6 周): Git 集成
- [ ] **第四阶段** (第 7-8 周): 微信导出
- [ ] **第五阶段** (第 9-10 周): 实时协作
- [ ] **第六阶段** (第 11-12 周): 插件生态

详细开发计划请查看 [开发计划.md](./开发计划.md)

## 📊 当前进度

第一阶段开发已完成，包括：

- ✅ 基础编辑器功能
- ✅ Hybrid 编辑模式
- ✅ Markdown 预览
- ✅ 本地存储系统
- ✅ 主题切换

查看详细进度：[开发进度报告-第一阶段.md](./开发进度报告-第一阶段.md)

## 🧪 功能测试

### 快速测试

运行自动化测试脚本：

```bash
# 启动功能测试（包含环境检查、构建测试、测试指导）
npm run test:features:open
```

### 手动测试

1. **启动应用**：`npm run dev`
2. **访问**：http://localhost:3000
3. **按照测试指导进行验证**：参考 [开发进度报告-第一阶段.md](./开发进度报告-第一阶段.md) 中的详细测试指导
4. **填写测试报告**：使用 [测试报告模板.md](./测试报告模板.md)

### 测试重点

- ✅ 编辑器基础功能（输入、高亮、响应速度）
- ✅ Hybrid 编辑模式（所见即所得体验）
- ✅ 实时预览同步（数学公式、图表、代码块）
- ✅ 本地存储系统（自动保存、文件管理）
- ✅ 界面交互（主题切换、响应式布局）
- ✅ 性能表现（大文档编辑、内存使用）

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

感谢以下开源项目：

- [CodeMirror](https://codemirror.net/) - 强大的代码编辑器
- [Next.js](https://nextjs.org/) - React 全栈框架
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [markdown-it](https://github.com/markdown-it/markdown-it) - Markdown 解析器
