'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface LayoutStore {
  // 侧边栏状态
  sidebarOpen: boolean
  sidebarTab: 'outline' | 'files' | 'history'
  
  // 预览模式
  previewMode: 'hidden' | 'split' | 'preview-only'
  
  // 编辑器设置
  hybridMode: boolean
  theme: 'light' | 'dark' | 'auto'
  keymap: 'default' | 'vim' | 'emacs'
  
  // 响应式状态
  isMobile: boolean
  
  // 操作
  toggleSidebar: () => void
  setSidebarTab: (tab: 'outline' | 'files' | 'history') => void
  setPreviewMode: (mode: 'hidden' | 'split' | 'preview-only') => void
  toggleHybridMode: () => void
  setTheme: (theme: 'light' | 'dark' | 'auto') => void
  setKeymap: (keymap: 'default' | 'vim' | 'emacs') => void
  setIsMobile: (isMobile: boolean) => void
}

export const useLayoutStore = create<LayoutStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      sidebarOpen: true,
      sidebarTab: 'outline',
      previewMode: 'split',
      hybridMode: false,
      theme: 'auto',
      keymap: 'default',
      isMobile: false,

      // 操作
      toggleSidebar: () => {
        set({ sidebarOpen: !get().sidebarOpen })
      },

      setSidebarTab: (tab) => {
        set({ sidebarTab: tab })
        // 如果侧边栏关闭，自动打开
        if (!get().sidebarOpen) {
          set({ sidebarOpen: true })
        }
      },

      setPreviewMode: (mode) => {
        set({ previewMode: mode })
      },

      toggleHybridMode: () => {
        set({ hybridMode: !get().hybridMode })
      },

      setTheme: (theme) => {
        set({ theme })
        
        // 应用主题到 document
        if (typeof window !== 'undefined') {
          const root = window.document.documentElement
          
          if (theme === 'auto') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
            root.classList.toggle('dark', mediaQuery.matches)
          } else {
            root.classList.toggle('dark', theme === 'dark')
          }
        }
      },

      setKeymap: (keymap) => {
        set({ keymap })
      },

      setIsMobile: (isMobile) => {
        set({ isMobile })
        
        // 移动端自动调整布局
        if (isMobile) {
          const { previewMode } = get()
          if (previewMode === 'split') {
            set({ previewMode: 'hidden' })
          }
        }
      }
    }),
    {
      name: 'layout-store',
      partialize: (state) => ({
        sidebarOpen: state.sidebarOpen,
        sidebarTab: state.sidebarTab,
        previewMode: state.previewMode,
        hybridMode: state.hybridMode,
        theme: state.theme,
        keymap: state.keymap
      })
    }
  )
)
