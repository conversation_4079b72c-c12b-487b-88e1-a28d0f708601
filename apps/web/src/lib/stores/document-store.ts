'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { openDB, type IDBPDatabase } from 'idb'
import type { DocumentState } from '@xiehu/editor-core'

interface DocumentStore {
  // 状态
  currentDocument: DocumentState | null
  documents: DocumentState[]
  isLoading: boolean
  
  // 操作
  loadDocument: (id?: string) => Promise<void>
  saveDocument: (document: Partial<DocumentState>) => Promise<void>
  createDocument: (title?: string) => Promise<DocumentState>
  deleteDocument: (id: string) => Promise<void>
  updateDocument: (id: string, updates: Partial<DocumentState>) => void
  setCurrentDocument: (document: DocumentState | null) => void
}

// IndexedDB 数据库管理
class DocumentDB {
  private db: IDBPDatabase | null = null
  private readonly dbName = 'xiehu-editor'
  private readonly version = 1

  async init() {
    if (this.db) return this.db

    this.db = await openDB(this.dbName, this.version, {
      upgrade(db) {
        // 文档存储
        if (!db.objectStoreNames.contains('documents')) {
          const documentStore = db.createObjectStore('documents', {
            keyPath: 'id'
          })
          documentStore.createIndex('updatedAt', 'updatedAt')
          documentStore.createIndex('title', 'title')
        }

        // 设置存储
        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', {
            keyPath: 'key'
          })
        }
      }
    })

    return this.db
  }

  async getAllDocuments(): Promise<DocumentState[]> {
    const db = await this.init()
    return db.getAll('documents')
  }

  async getDocument(id: string): Promise<DocumentState | undefined> {
    const db = await this.init()
    return db.get('documents', id)
  }

  async saveDocument(document: DocumentState): Promise<void> {
    const db = await this.init()
    await db.put('documents', document)
  }

  async deleteDocument(id: string): Promise<void> {
    const db = await this.init()
    await db.delete('documents', id)
  }
}

const documentDB = new DocumentDB()

export const useDocumentStore = create<DocumentStore>()(
  persist(
    (set, get) => ({
      currentDocument: null,
      documents: [],
      isLoading: false,

      loadDocument: async (id?: string) => {
        set({ isLoading: true })
        
        try {
          if (id) {
            const document = await documentDB.getDocument(id)
            if (document) {
              set({ currentDocument: document, isLoading: false })
              return
            }
          }

          // 加载所有文档
          const documents = await documentDB.getAllDocuments()
          set({ documents })

          // 如果没有指定 ID，加载最近编辑的文档
          if (!id && documents.length > 0) {
            const latest = documents.sort((a, b) => 
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            )[0]
            set({ currentDocument: latest })
          } else if (!id) {
            // 创建默认文档
            const defaultDoc = await get().createDocument('欢迎使用写乎')
            set({ currentDocument: defaultDoc })
          }
        } catch (error) {
          console.error('加载文档失败:', error)
        } finally {
          set({ isLoading: false })
        }
      },

      saveDocument: async (updates: Partial<DocumentState>) => {
        const { currentDocument } = get()
        if (!currentDocument) return

        const updatedDocument: DocumentState = {
          ...currentDocument,
          ...updates,
          updatedAt: new Date(),
          saved: true
        }

        try {
          await documentDB.saveDocument(updatedDocument)
          set({ currentDocument: updatedDocument })
          
          // 更新文档列表
          const documents = get().documents
          const index = documents.findIndex(doc => doc.id === updatedDocument.id)
          if (index >= 0) {
            documents[index] = updatedDocument
          } else {
            documents.push(updatedDocument)
          }
          set({ documents: [...documents] })
        } catch (error) {
          console.error('保存文档失败:', error)
        }
      },

      createDocument: async (title = '新文档') => {
        const now = new Date()
        const newDocument: DocumentState = {
          id: `doc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          title,
          content: `# ${title}\n\n开始编写你的内容...`,
          createdAt: now,
          updatedAt: now,
          saved: false,
          cursorPos: 0
        }

        try {
          await documentDB.saveDocument(newDocument)
          const documents = [...get().documents, newDocument]
          set({ documents, currentDocument: newDocument })
          return newDocument
        } catch (error) {
          console.error('创建文档失败:', error)
          throw error
        }
      },

      deleteDocument: async (id: string) => {
        try {
          await documentDB.deleteDocument(id)
          const documents = get().documents.filter(doc => doc.id !== id)
          set({ documents })
          
          // 如果删除的是当前文档，切换到其他文档
          const { currentDocument } = get()
          if (currentDocument?.id === id) {
            if (documents.length > 0) {
              set({ currentDocument: documents[0] })
            } else {
              const newDoc = await get().createDocument()
              set({ currentDocument: newDoc })
            }
          }
        } catch (error) {
          console.error('删除文档失败:', error)
        }
      },

      updateDocument: (id: string, updates: Partial<DocumentState>) => {
        const documents = get().documents
        const index = documents.findIndex(doc => doc.id === id)
        if (index >= 0) {
          documents[index] = { ...documents[index], ...updates }
          set({ documents: [...documents] })
          
          // 如果更新的是当前文档
          const { currentDocument } = get()
          if (currentDocument?.id === id) {
            set({ currentDocument: { ...currentDocument, ...updates } })
          }
        }
      },

      setCurrentDocument: (document: DocumentState | null) => {
        set({ currentDocument: document })
      }
    }),
    {
      name: 'document-store',
      partialize: (state) => ({
        // 只持久化基本信息，文档内容存储在 IndexedDB
        documents: state.documents.map(doc => ({
          id: doc.id,
          title: doc.title,
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt,
          saved: doc.saved
        }))
      })
    }
  )
)
