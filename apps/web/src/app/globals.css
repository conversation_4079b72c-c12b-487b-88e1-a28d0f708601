@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* CodeMirror 编辑器样式 */
.cm-editor {
  @apply h-full;
}

.cm-focused {
  @apply outline-none;
}

.cm-content {
  @apply p-4 text-sm leading-relaxed;
  font-family: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', 'SF Mono', Monaco, 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
}

.cm-line {
  @apply min-h-[1.5rem];
}

/* Markdown 预览样式 */
.markdown-preview {
  @apply prose prose-slate dark:prose-invert max-w-none;
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  @apply font-semibold;
}

.markdown-preview code {
  @apply bg-muted px-1 py-0.5 rounded text-sm;
}

.markdown-preview pre {
  @apply bg-muted p-4 rounded-lg overflow-x-auto;
}

.markdown-preview blockquote {
  @apply border-l-4 border-primary pl-4 italic;
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  @apply w-2 h-2;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-muted rounded;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded hover:bg-muted-foreground/50;
}

/* 布局样式 */
.editor-layout {
  height: calc(100vh - 56px); /* 减去顶部导航高度 */
}

.sidebar-transition {
  transition: transform 0.3s ease-in-out;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .editor-layout {
    height: calc(100vh - 56px - 48px); /* 减去顶部导航和底部操作栏 */
  }
}
