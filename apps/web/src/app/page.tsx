'use client'

import { useState, useEffect } from 'react'
import { Header } from '@/components/header'
import { Sidebar } from '@/components/sidebar'
import { Editor } from '@/components/editor'
import { Preview } from '@/components/preview'
import { StatusBar } from '@/components/status-bar'
import { useDocumentStore } from '@/lib/stores/document-store'
import { useLayoutStore } from '@/lib/stores/layout-store'

export default function HomePage() {
  const { currentDocument, loadDocument } = useDocumentStore()
  const { sidebarOpen, previewMode } = useLayoutStore()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // 加载默认文档或最后编辑的文档
    loadDocument()
  }, [loadDocument])

  if (!mounted) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen flex-col bg-background">
      <Header />
      
      <div className="flex flex-1 overflow-hidden">
        {/* 侧边栏 */}
        <Sidebar />
        
        {/* 主编辑区域 */}
        <div className="flex flex-1 overflow-hidden">
          {/* 编辑器 */}
          <div className={`flex-1 ${previewMode === 'split' ? 'w-[70%]' : 'w-full'}`}>
            <Editor />
          </div>
          
          {/* 预览区 */}
          {previewMode === 'split' && (
            <div className="w-[30%] border-l border-border">
              <Preview />
            </div>
          )}
        </div>
      </div>
      
      <StatusBar />
    </div>
  )
}
