'use client'

import { useEffect, useState, useMemo } from 'react'
import { MarkdownRenderer } from '@xiehu/renderer'
import { useDocumentStore } from '@/lib/stores/document-store'
import { useLayoutStore } from '@/lib/stores/layout-store'

export function Preview() {
  const { currentDocument } = useDocumentStore()
  const { theme } = useLayoutStore()
  const [renderResult, setRenderResult] = useState<{
    html: string
    renderTime: number
    hasError: boolean
    error?: string
  } | null>(null)

  // 创建渲染器实例
  const renderer = useMemo(() => {
    return new MarkdownRenderer({
      katex: true,
      mermaid: true,
      emoji: true,
      toc: true,
      anchor: true,
      sanitize: true
    })
  }, [])

  // 渲染 Markdown
  useEffect(() => {
    if (!currentDocument?.content) {
      setRenderResult(null)
      return
    }

    const startTime = performance.now()
    
    try {
      const result = renderer.render(currentDocument.content)
      setRenderResult({
        html: result.html,
        renderTime: result.renderTime,
        hasError: result.hasError,
        error: result.error
      })
    } catch (error) {
      setRenderResult({
        html: `<div class="error">渲染失败: ${error instanceof Error ? error.message : '未知错误'}</div>`,
        renderTime: performance.now() - startTime,
        hasError: true,
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }, [currentDocument?.content, renderer])

  // 处理 Mermaid 图表渲染
  useEffect(() => {
    if (!renderResult?.html || renderResult.hasError) return

    // 查找所有 Mermaid 图表并渲染
    const mermaidElements = document.querySelectorAll('.markdown-mermaid')
    
    mermaidElements.forEach(async (element) => {
      const preElement = element.querySelector('pre')
      const loadingElement = element.querySelector('.markdown-mermaid-loading')
      
      if (!preElement || !loadingElement) return

      const content = preElement.textContent || ''
      
      try {
        // 动态导入 Mermaid
        const { default: mermaid } = await import('mermaid')
        
        // 初始化 Mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: theme === 'dark' ? 'dark' : 'default',
          securityLevel: 'loose'
        })

        // 渲染图表
        const { svg } = await mermaid.render(
          `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          content
        )
        
        loadingElement.innerHTML = svg
      } catch (error) {
        loadingElement.innerHTML = `
          <div class="error p-4 border border-destructive rounded">
            <p class="text-destructive font-medium">图表渲染失败</p>
            <p class="text-sm text-muted-foreground mt-1">${error instanceof Error ? error.message : '未知错误'}</p>
          </div>
        `
      }
    })
  }, [renderResult, theme])

  // 处理代码复制
  useEffect(() => {
    if (!renderResult?.html) return

    const handleCopyClick = (e: Event) => {
      const target = e.target as HTMLElement
      if (!target.classList.contains('markdown-copy-btn')) return

      const code = target.getAttribute('data-copy')
      if (!code) return

      navigator.clipboard.writeText(code).then(() => {
        // 显示复制成功提示
        const originalText = target.textContent
        target.textContent = '已复制'
        setTimeout(() => {
          target.textContent = originalText
        }, 1000)
      }).catch(() => {
        // 显示复制失败提示
        const originalText = target.textContent
        target.textContent = '复制失败'
        setTimeout(() => {
          target.textContent = originalText
        }, 1000)
      })
    }

    document.addEventListener('click', handleCopyClick)
    return () => document.removeEventListener('click', handleCopyClick)
  }, [renderResult])

  if (!currentDocument) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          没有可预览的内容
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 预览工具栏 */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">预览</span>
        </div>
        
        <div className="flex items-center space-x-2">
          {renderResult && (
            <div className="text-xs text-muted-foreground">
              渲染耗时: {renderResult.renderTime.toFixed(1)}ms
            </div>
          )}
          
          {renderResult?.hasError && (
            <div className="text-xs text-destructive">
              渲染错误
            </div>
          )}
        </div>
      </div>

      {/* 预览内容 */}
      <div className="flex-1 overflow-auto custom-scrollbar">
        {renderResult ? (
          <div 
            className="markdown-preview p-6"
            dangerouslySetInnerHTML={{ __html: renderResult.html }}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">正在渲染...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
