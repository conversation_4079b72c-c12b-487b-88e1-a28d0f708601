'use client'

import { useDocumentStore } from '@/lib/stores/document-store'
import { useLayoutStore } from '@/lib/stores/layout-store'

export function StatusBar() {
  const { currentDocument } = useDocumentStore()
  const { hybridMode, theme, keymap } = useLayoutStore()

  const wordCount = currentDocument?.content.length || 0
  const lineCount = currentDocument?.content.split('\n').length || 0

  return (
    <div className="h-6 border-t border-border bg-muted/50 flex items-center justify-between px-4 text-xs text-muted-foreground">
      <div className="flex items-center space-x-4">
        <span>{wordCount} 字符</span>
        <span>{lineCount} 行</span>
        {currentDocument && (
          <span>
            {currentDocument.saved ? '已保存' : '未保存'}
          </span>
        )}
      </div>
      
      <div className="flex items-center space-x-4">
        <span>模式: {hybridMode ? '混合' : '源码'}</span>
        <span>主题: {theme}</span>
        <span>键位: {keymap}</span>
      </div>
    </div>
  )
}
