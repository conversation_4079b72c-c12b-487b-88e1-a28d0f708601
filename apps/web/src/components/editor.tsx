'use client'

import { useEffect, useRef, useCallback } from 'react'
import { createEditor, type EditorInstance } from '@xiehu/editor-core'
import { useDocumentStore } from '@/lib/stores/document-store'
import { useLayoutStore } from '@/lib/stores/layout-store'

export function Editor() {
  const editorRef = useRef<HTMLDivElement>(null)
  const editorInstanceRef = useRef<EditorInstance | null>(null)
  
  const { currentDocument, saveDocument, updateDocument } = useDocumentStore()
  const { hybridMode, theme, keymap } = useLayoutStore()

  // 文档变更处理
  const handleDocumentChange = useCallback((content: string) => {
    if (!currentDocument) return
    
    // 标记文档为未保存状态
    updateDocument(currentDocument.id, {
      content,
      saved: false,
      updatedAt: new Date()
    })
  }, [currentDocument, updateDocument])

  // 光标位置变更处理
  const handleCursorChange = useCallback((pos: number) => {
    if (!currentDocument) return
    
    updateDocument(currentDocument.id, {
      cursorPos: pos
    })
  }, [currentDocument, updateDocument])

  // 自动保存
  const handleAutoSave = useCallback(async (content: string) => {
    if (!currentDocument) return
    
    await saveDocument({
      content,
      updatedAt: new Date()
    })
  }, [currentDocument, saveDocument])

  // 初始化编辑器
  useEffect(() => {
    if (!editorRef.current || !currentDocument) return

    // 清理之前的编辑器实例
    if (editorInstanceRef.current) {
      editorInstanceRef.current.destroy()
    }

    // 创建新的编辑器实例
    editorInstanceRef.current = createEditor(editorRef.current, {
      initialDoc: currentDocument.content,
      hybridMode,
      theme: theme === 'auto' ? 'light' : theme, // TODO: 检测系统主题
      keymap,
      autoSaveInterval: 3000,
      onChange: handleDocumentChange,
      onCursorChange: handleCursorChange
    })

    // 恢复光标位置
    if (currentDocument.cursorPos) {
      editorInstanceRef.current.setCursor(currentDocument.cursorPos)
    }

    // 聚焦编辑器
    editorInstanceRef.current.focus()

    return () => {
      if (editorInstanceRef.current) {
        editorInstanceRef.current.destroy()
        editorInstanceRef.current = null
      }
    }
  }, [currentDocument?.id]) // 只在文档 ID 变化时重新初始化

  // 更新编辑器配置
  useEffect(() => {
    if (!editorInstanceRef.current) return

    // 更新主题
    const actualTheme = theme === 'auto' 
      ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
      : theme
    
    editorInstanceRef.current.setTheme(actualTheme)
  }, [theme])

  useEffect(() => {
    if (!editorInstanceRef.current) return

    // 更新 Hybrid 模式
    editorInstanceRef.current.toggleHybridMode()
  }, [hybridMode])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + S 保存
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        if (currentDocument && editorInstanceRef.current) {
          handleAutoSave(editorInstanceRef.current.getDoc())
        }
      }

      // Ctrl/Cmd + / 切换 Hybrid 模式
      if ((e.ctrlKey || e.metaKey) && e.key === '/') {
        e.preventDefault()
        useLayoutStore.getState().toggleHybridMode()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [currentDocument, handleAutoSave])

  // 监听系统主题变化
  useEffect(() => {
    if (theme !== 'auto') return

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleThemeChange = (e: MediaQueryListEvent) => {
      if (editorInstanceRef.current) {
        editorInstanceRef.current.setTheme(e.matches ? 'dark' : 'light')
      }
    }

    mediaQuery.addEventListener('change', handleThemeChange)
    return () => mediaQuery.removeEventListener('change', handleThemeChange)
  }, [theme])

  if (!currentDocument) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <div className="text-muted-foreground">没有打开的文档</div>
          <button 
            className="mt-2 text-primary hover:underline"
            onClick={() => useDocumentStore.getState().createDocument()}
          >
            创建新文档
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 编辑器工具栏 */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{currentDocument.title}</span>
          {!currentDocument.saved && (
            <span className="text-xs text-muted-foreground">●未保存</span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            className="text-xs px-2 py-1 rounded hover:bg-muted"
            onClick={() => useLayoutStore.getState().toggleHybridMode()}
          >
            {hybridMode ? '源码' : '混合'}
          </button>
          
          <div className="text-xs text-muted-foreground">
            {currentDocument.content.length} 字符
          </div>
        </div>
      </div>

      {/* 编辑器主体 */}
      <div 
        ref={editorRef} 
        className="flex-1 overflow-hidden custom-scrollbar"
      />
    </div>
  )
}
