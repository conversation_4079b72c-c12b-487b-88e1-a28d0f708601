'use client'

import { useLayoutStore } from '@/lib/stores/layout-store'
import { useDocumentStore } from '@/lib/stores/document-store'

export function Header() {
  const { toggleSidebar, setPreviewMode, previewMode, theme, setTheme } = useLayoutStore()
  const { currentDocument } = useDocumentStore()

  return (
    <header className="h-14 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center justify-between h-full px-4">
        {/* 左侧 */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <h1 className="text-lg font-semibold">写乎</h1>
            <span className="text-xs text-muted-foreground">v0.1.0</span>
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={toggleSidebar}
              className="p-2 hover:bg-muted rounded-md"
              title="切换侧边栏"
            >
              📂
            </button>
            
            <span className="text-sm text-muted-foreground">
              {currentDocument?.title || '未命名文档'}
            </span>
          </div>
        </div>

        {/* 右侧 */}
        <div className="flex items-center space-x-2">
          {/* 预览模式切换 */}
          <div className="flex items-center border rounded-md">
            <button
              onClick={() => setPreviewMode('hidden')}
              className={`px-3 py-1 text-sm ${previewMode === 'hidden' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
            >
              编辑
            </button>
            <button
              onClick={() => setPreviewMode('split')}
              className={`px-3 py-1 text-sm ${previewMode === 'split' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
            >
              分栏
            </button>
            <button
              onClick={() => setPreviewMode('preview-only')}
              className={`px-3 py-1 text-sm ${previewMode === 'preview-only' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}`}
            >
              预览
            </button>
          </div>

          {/* 主题切换 */}
          <button
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            className="p-2 hover:bg-muted rounded-md"
            title="切换主题"
          >
            {theme === 'dark' ? '🌙' : '☀️'}
          </button>
        </div>
      </div>
    </header>
  )
}
