'use client'

import * as React from 'react'

// 简化的 Toast 实现
export function Toaster() {
  return <div id="toast-container" className="fixed top-4 right-4 z-50" />
}

export function useToast() {
  const toast = React.useCallback((message: string, type: 'success' | 'error' | 'info' = 'info') => {
    const container = document.getElementById('toast-container')
    if (!container) return

    const toastElement = document.createElement('div')
    toastElement.className = `
      mb-2 p-3 rounded-md shadow-lg transition-all duration-300 transform translate-x-full
      ${type === 'success' ? 'bg-green-500 text-white' : ''}
      ${type === 'error' ? 'bg-red-500 text-white' : ''}
      ${type === 'info' ? 'bg-blue-500 text-white' : ''}
    `
    toastElement.textContent = message

    container.appendChild(toastElement)

    // 动画显示
    setTimeout(() => {
      toastElement.classList.remove('translate-x-full')
    }, 10)

    // 自动移除
    setTimeout(() => {
      toastElement.classList.add('translate-x-full')
      setTimeout(() => {
        container.removeChild(toastElement)
      }, 300)
    }, 3000)
  }, [])

  return { toast }
}
