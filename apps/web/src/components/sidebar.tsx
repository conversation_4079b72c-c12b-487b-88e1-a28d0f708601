'use client'

import { useLayoutStore } from '@/lib/stores/layout-store'
import { useDocumentStore } from '@/lib/stores/document-store'

export function Sidebar() {
  const { sidebarOpen, sidebarTab, setSidebarTab } = useLayoutStore()
  const { documents, currentDocument, setCurrentDocument, createDocument, deleteDocument } = useDocumentStore()

  if (!sidebarOpen) return null

  return (
    <div className="w-64 border-r border-border bg-background flex flex-col">
      {/* 标签页 */}
      <div className="flex border-b border-border">
        <button
          onClick={() => setSidebarTab('outline')}
          className={`flex-1 px-3 py-2 text-sm ${sidebarTab === 'outline' ? 'bg-muted' : 'hover:bg-muted/50'}`}
        >
          🗂️ 目录
        </button>
        <button
          onClick={() => setSidebarTab('files')}
          className={`flex-1 px-3 py-2 text-sm ${sidebarTab === 'files' ? 'bg-muted' : 'hover:bg-muted/50'}`}
        >
          📂 文件
        </button>
      </div>

      {/* 内容区 */}
      <div className="flex-1 overflow-auto">
        {sidebarTab === 'files' && (
          <div className="p-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">文档列表</span>
              <button
                onClick={() => createDocument()}
                className="text-xs px-2 py-1 bg-primary text-primary-foreground rounded hover:bg-primary/90"
              >
                新建
              </button>
            </div>
            
            <div className="space-y-1">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className={`p-2 rounded cursor-pointer group ${
                    currentDocument?.id === doc.id ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
                  }`}
                  onClick={() => setCurrentDocument(doc)}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm truncate">{doc.title}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        deleteDocument(doc.id)
                      }}
                      className="opacity-0 group-hover:opacity-100 text-xs hover:text-destructive"
                    >
                      ×
                    </button>
                  </div>
                  <div className="text-xs opacity-70 mt-1">
                    {new Date(doc.updatedAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {sidebarTab === 'outline' && (
          <div className="p-2">
            <div className="text-sm font-medium mb-2">文档大纲</div>
            <div className="text-xs text-muted-foreground">
              大纲功能开发中...
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
