{"name": "@xiehu/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@codemirror/commands": "^6.3.0", "@codemirror/lang-markdown": "^6.2.0", "@codemirror/language": "^6.9.1", "@codemirror/search": "^6.5.4", "@codemirror/state": "^6.3.1", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.21.3", "@tailwindcss/typography": "^0.5.16", "@xiehu/editor-core": "workspace:*", "@xiehu/renderer": "workspace:*", "@xiehu/ui": "workspace:*", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "codemirror": "^6.0.1", "idb": "^7.1.1", "markdown-it": "^13.0.1", "markdown-it-emoji": "^2.0.2", "markdown-it-katex": "^2.0.3", "mermaid": "^10.6.1", "next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.1.0", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.7"}, "devDependencies": {"@types/markdown-it": "^13.0.7", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "postcss": "^8.4.32", "typescript": "^5.3.3"}}