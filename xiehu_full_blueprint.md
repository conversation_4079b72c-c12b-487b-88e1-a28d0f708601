# 写乎 Markdown 编辑器 · 全景开发蓝图  
> **Local‑first × Git 工作流 × 所见即所得 × 公众号排版**  
> 面向工程师的“一份文档走完全部流程”  
> 版本：v2.1 更新时间：2025‑07‑28  

---

## 目录
1. [项目定位](#项目定位)  
2. [技术栈一览](#技术栈一览)  
3. [功能模块与验收标准](#功能模块与验收标准)  
4. [UI 交互总览](#ui-交互总览)  
5. [系统架构图](#系统架构图)  
6. [核心实现要点](#核心实现要点)  
7. [PWA & 离线策略](#pwa--离线策略)  
8. [安全与合规](#安全与合规)  
9. [监控与性能指标](#监控与性能指标)  
10. [12 周开发排期](#12 周开发排期)  
11. [仓库结构与约定](#仓库结构与约定)  

---

## 项目定位
| 维度         | 描述 |
|--------------|------|
| **目标用户** | 技术写作者、开源维护者、公众号运营、小团队协作 |
| **核心卖点** | 离线可写 · 所见即所得 · Git 原生 · 微信富文本一键导出 |
| **差异化**   | 同时解决 *离线 + Git + 协作 + 公众号排版* 四件痛点 |

---

## 技术栈一览
| 层次           | 主要选型                                   | 说明 |
|----------------|--------------------------------------------|------|
| **前端框架**   | **Next.js 20.x (React 19 · App Router)**    | RSC + Edge SSR + PWA 插件 |
| 编辑器内核     | **CodeMirror 6**                           | Decoration API 支持 Hybrid |
| Markdown 渲染  | **markdown‑it** + `emoji/katex/mermaid`    | WebWorker 内解析 |
| 实时协作       | **Yjs** + `y-websocket` / PartyKit Relay   | CRDT 无冲突 |
| Git 工作流     | **isomorphic‑git** + `lightning-fs`        | 浏览器端 commit/push |
| 离线缓存       | **Workbox SW** + **IndexedDB (idb)**       | 页面+数据离线 |
| 样式/组件      | **Tailwind CSS** + **shadcn/ui**           | 暗黑 & 主题切换 |
| 安全审查       | **DOMPurify** + **ONNX Runtime Web**       | XSS & 违规本地检测 |
| 监控部署       | **Sentry** + Vercel / Cloudflare Pages     | CI/CD + Trace |

---

## 功能模块与验收标准

| 模块 | 目标 | 关键行为 | 技术要点 | 验收标准 |
|------|------|----------|----------|----------|
| **编辑核心** | 100 k 字不卡 | Hybrid 行内 WYSIWYG；分栏写看；Vim/Emacs | CM6 Decoration + 虚拟滚动 | 光标耗时 ≤ 5 ms；模式切换 ≤ 150 ms |
| **预览排版** | 所见即所得一致 | KaTeX / Mermaid / PlantUML；粘贴图即上传 | Worker 渲染 + IO 懒加载 | Mermaid 200 行首帧 ≤ 800 ms |
| **目录导航** | 快速定位长文 | 自动 H1–H3；折叠/搜索；滚动高亮 | CM6 syntaxTree；IO + RAF | 刷新 ≤ 2 ms/千行；高亮 120 fps |
| **实时协作** | 0 冲突、多端离线 | 光标 & 选区；@评论；离线合并 | Yjs + IndexedDB buffer | 延迟 ≤ 200 ms（50 人） |
| **Git 集成** | 保存 = commit | OAuth；push/pull；版本 Diff | isomorphic‑git | clone 10 MB ≤ 3 s；push ≤ 1 s |
| **离线同步** | 无网也能写 | SW 缓存；BG Sync push | Workbox；gitQueue | 断网刷新可写；重连 3 s 内同步 |
| **公众号输出** | 一键富文本 | 代码复制按钮；1080 px 压图 | HTML 白名单转换器 | 微信粘贴 100 % 正常 |
| **插件生态** | VSCode 式扩展 | JSON 清单；命令/面板钩子 | ESModule + iframe | 插件热装卸不崩主线程 |

---

## UI 交互总览

```
┌──────── 顶部导航 56 px ──────────────────────────────────────────────┐
│ Logo │ 草稿 / Doc.md ●已保存 ─── 🗂️目录 │ 📂文件 │ 🔍搜索 │ 发布 │ 👤 │
└────────────────────────────────────────────────────────────────────┘
┌─ 主导航 48 px ─┬─ 功能抽屉 240 px (目录默认) ─┬──── 编辑区 ───┬─ 预览 30% ─┐
│ 📝 片段        │ H1–H3 树 · 折叠/搜索        │ Markdown 源   │ Rendered   │
│ 📂 文件        │ 文件树 / 历史              │ (Hybrid 可切) │ HTML       │
│ ⏳ 版本        │ …                          │               │            │
└────────────────┴────────────────────────────┴────────────────┴───────────┘
                                 ⤵︎ Status Dock：字数 · 分支 · 主题
```
*768–1279 px*：抽屉滑出、预览改 Tab； *≤ 767 px*：单栏编辑 + 底部 ActionSheet。

---

## 系统架构图
```mermaid
graph TD
  A[CodeMirror 6] --> B[IndexedDB]
  A --> C[Service Worker Cache]
  B --> D(lightning‑fs)
  D --> E(isomorphic‑git)
  A -.-> F[Yjs CRDT]
  F -->|WebSocket| G[Relay / PartyKit]
  E --> H[GitHub / Gitea]
  G --> I[PostgreSQL 影子表]
```

---

## 核心实现要点
### Hybrid 编辑
- Decoration.widget 覆盖非光标段落  
- 段落缓存 Map；节流 100 ms

### 目录抽屉
- `syntaxTree()` 抽取 H1–H3  
- IO高亮；搜索过滤

### Git 队列
- commit 保存 `localStorage.gitQueue`  
- SW `sync` 批量 `git.push`

---

## PWA & 离线策略
- Workbox 预缓存；`NetworkFirst` for `.md`  
- `beforeinstallprompt` 安装提示  
- 离线编辑 → 在线 3 s 内自动同步

---

## 安全与合规
- DOMPurify XSS 清洗  
- CSP Nonce；iframe 域名白名单  
- 本地 ONNX 违规图文审查  
- 企业版：词库 + 审计 Webhook

---

## 监控与性能指标
| 指标 | 目标 | 工具 |
|------|------|------|
| LCP  | ≤ 1 s | WebVitals |
| 输入延迟 | ≤ 50 ms | PerfObserver |
| JS Error | ≤ 0.1 % | Sentry |
| PWA 覆盖 | ≥ 95 % | Workbox |

---

## 12 周开发排期
| 周 | 里程碑 | 交付 |
|----|--------|------|
| 1‑2 | MVP   | Hybrid + 预览 + IndexedDB |
| 3‑4 | PWA   | ServiceWorker + 安装提示 |
| 5‑6 | Git   | OAuth + push/pull + Diff |
| 7‑8 | 微信  | 富文本导出 + 代码复制 |
| 9‑10| 协作  | Yjs 光标 + 评论合并 |
| 11‑12| Beta | 插件 SDK + AI 审核 |

---

## 仓库结构与约定
```
root/
├ apps/web/        # Next.js 20.x
│ └ app/           # App Router
├ packages/        # editor-core / renderer / outline / ui ...
└ scripts/         # CI / release
```
- **npm Workspaces**  
- PR 格式 `feat(scope): msg #issue` · squash merge  
- `vitest` 覆盖率 ≥ 80 %  
- Pre‑commit：ESLint + Prettier + lint-staged

---


