{"name": "xiehu-editor", "version": "0.1.0", "description": "写乎 Markdown 编辑器 - Local-first × Git 工作流 × 所见即所得 × 公众号排版", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "dev:open": "node scripts/dev.js", "build": "turbo run build", "test": "turbo run test", "test:build": "node scripts/test-build.js", "test:features": "node scripts/test-features.js", "test:features:open": "node scripts/test-features.js --open", "lint": "turbo run lint", "lint:fix": "turbo run lint -- --fix", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/node": "^20.10.5", "eslint": "^8.56.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/xiehu-editor/xiehu-editor.git"}, "keywords": ["markdown", "editor", "wysiwyg", "git", "collaboration", "pwa", "local-first"], "author": "Xiehu Team", "license": "MIT"}