import { EditorView, basicSetup } from 'codemirror'
import { EditorState, Compartment } from '@codemirror/state'
import { markdown } from '@codemirror/lang-markdown'
import { oneDark } from '@codemirror/theme-one-dark'
import { vim } from '@codemirror/legacy-modes/mode/vim'
import { keymap } from '@codemirror/view'
import { defaultKeymap, historyKeymap, searchKeymap } from '@codemirror/commands'
import { searchKeymap as search } from '@codemirror/search'

import type { EditorConfig, EditorInstance } from './types'
import { HybridExtension } from './extensions/hybrid'
import { MarkdownExtension } from './extensions/markdown'
import { ThemeExtension } from './extensions/theme'

export function createEditor(
  parent: HTMLElement,
  config: EditorConfig = {}
): EditorInstance {
  const {
    initialDoc = '',
    hybridMode = false,
    theme = 'light',
    readOnly = false,
    keymap: keymapMode = 'default',
    autoSaveInterval = 3000,
    onChange,
    onCursorChange
  } = config

  // 创建配置隔间，用于动态更新
  const themeCompartment = new Compartment()
  const hybridCompartment = new Compartment()
  const readOnlyCompartment = new Compartment()

  // 构建扩展数组
  const extensions = [
    basicSetup,
    markdown(),
    MarkdownExtension(),
    themeCompartment.of(theme === 'dark' ? oneDark : []),
    hybridCompartment.of(hybridMode ? HybridExtension() : []),
    readOnlyCompartment.of(EditorState.readOnly.of(readOnly)),
    keymap.of([
      ...defaultKeymap,
      ...historyKeymap,
      ...searchKeymap,
    ]),
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        onChange?.(update.state.doc.toString())
      }
      if (update.selectionSet) {
        onCursorChange?.(update.state.selection.main.head)
      }
    }),
    EditorView.theme({
      '&': {
        height: '100%',
        fontSize: '14px',
      },
      '.cm-content': {
        padding: '16px',
        lineHeight: '1.6',
        fontFamily: '"JetBrains Mono", "Fira Code", monospace',
      },
      '.cm-focused': {
        outline: 'none',
      },
      '.cm-editor': {
        height: '100%',
      },
      '.cm-scroller': {
        height: '100%',
      }
    })
  ]

  // 创建编辑器状态
  const state = EditorState.create({
    doc: initialDoc,
    extensions
  })

  // 创建编辑器视图
  const view = new EditorView({
    state,
    parent
  })

  // 自动保存定时器
  let autoSaveTimer: NodeJS.Timeout | null = null
  if (autoSaveInterval > 0 && onChange) {
    const startAutoSave = () => {
      if (autoSaveTimer) clearTimeout(autoSaveTimer)
      autoSaveTimer = setTimeout(() => {
        onChange(view.state.doc.toString())
      }, autoSaveInterval)
    }

    view.dispatch({
      effects: EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          startAutoSave()
        }
      })
    })
  }

  // 返回编辑器实例
  const instance: EditorInstance = {
    view,

    getDoc() {
      return view.state.doc.toString()
    },

    setDoc(doc: string) {
      view.dispatch({
        changes: {
          from: 0,
          to: view.state.doc.length,
          insert: doc
        }
      })
    },

    getCursor() {
      return view.state.selection.main.head
    },

    setCursor(pos: number) {
      view.dispatch({
        selection: { anchor: pos, head: pos }
      })
    },

    getSelection() {
      const { from, to } = view.state.selection.main
      return view.state.doc.sliceString(from, to)
    },

    setSelection(from: number, to?: number) {
      view.dispatch({
        selection: { anchor: from, head: to ?? from }
      })
    },

    insertText(text: string, pos?: number) {
      const insertPos = pos ?? view.state.selection.main.head
      view.dispatch({
        changes: {
          from: insertPos,
          insert: text
        },
        selection: { anchor: insertPos + text.length }
      })
    },

    replaceText(from: number, to: number, text: string) {
      view.dispatch({
        changes: {
          from,
          to,
          insert: text
        }
      })
    },

    focus() {
      view.focus()
    },

    destroy() {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
      view.destroy()
    },

    setTheme(newTheme: 'light' | 'dark') {
      view.dispatch({
        effects: themeCompartment.reconfigure(
          newTheme === 'dark' ? oneDark : []
        )
      })
    },

    toggleHybridMode() {
      const currentHybrid = view.state.facet(hybridCompartment)
      view.dispatch({
        effects: hybridCompartment.reconfigure(
          currentHybrid.length > 0 ? [] : HybridExtension()
        )
      })
    }
  }

  return instance
}
