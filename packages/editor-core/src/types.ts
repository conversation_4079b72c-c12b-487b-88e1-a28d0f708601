import { EditorView } from '@codemirror/view'
import { EditorState } from '@codemirror/state'

export interface EditorConfig {
  /** 初始文档内容 */
  initialDoc?: string
  /** 是否启用 Hybrid 模式 */
  hybridMode?: boolean
  /** 主题模式 */
  theme?: 'light' | 'dark' | 'auto'
  /** 是否只读 */
  readOnly?: boolean
  /** 键位绑定 */
  keymap?: 'default' | 'vim' | 'emacs'
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number
  /** 文档变更回调 */
  onChange?: (doc: string) => void
  /** 光标位置变更回调 */
  onCursorChange?: (pos: number) => void
}

export interface EditorInstance {
  /** CodeMirror 视图实例 */
  view: EditorView
  /** 获取当前文档内容 */
  getDoc(): string
  /** 设置文档内容 */
  setDoc(doc: string): void
  /** 获取当前光标位置 */
  getCursor(): number
  /** 设置光标位置 */
  setCursor(pos: number): void
  /** 获取选中文本 */
  getSelection(): string
  /** 设置选中文本 */
  setSelection(from: number, to?: number): void
  /** 插入文本 */
  insertText(text: string, pos?: number): void
  /** 替换文本 */
  replaceText(from: number, to: number, text: string): void
  /** 聚焦编辑器 */
  focus(): void
  /** 销毁编辑器 */
  destroy(): void
  /** 切换主题 */
  setTheme(theme: 'light' | 'dark'): void
  /** 切换 Hybrid 模式 */
  toggleHybridMode(): void
}

export interface HybridDecoration {
  /** 装饰类型 */
  type: 'heading' | 'bold' | 'italic' | 'code' | 'link' | 'image'
  /** 起始位置 */
  from: number
  /** 结束位置 */
  to: number
  /** 渲染内容 */
  content?: string
  /** 额外属性 */
  attrs?: Record<string, any>
}

export interface DocumentState {
  /** 文档 ID */
  id: string
  /** 文档标题 */
  title: string
  /** 文档内容 */
  content: string
  /** 创建时间 */
  createdAt: Date
  /** 更新时间 */
  updatedAt: Date
  /** 是否已保存 */
  saved: boolean
  /** 光标位置 */
  cursorPos?: number
}
