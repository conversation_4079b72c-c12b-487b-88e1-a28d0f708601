import { Extension } from '@codemirror/state'
import { Decoration, DecorationSet, EditorView, ViewPlugin, ViewUpdate, WidgetType } from '@codemirror/view'
import { syntaxTree } from '@codemirror/language'

/**
 * Hybrid 编辑模式扩展
 * 使用 Decoration API 实现所见即所得的 Markdown 编辑
 */

class HeadingWidget extends WidgetType {
  constructor(
    private level: number,
    private text: string
  ) {
    super()
  }

  toDOM() {
    const element = document.createElement(`h${this.level}`)
    element.textContent = this.text
    element.className = `cm-hybrid-heading cm-hybrid-h${this.level}`
    element.style.cssText = `
      margin: 0;
      padding: 8px 0;
      font-weight: 600;
      line-height: 1.4;
      color: var(--foreground);
      ${this.level === 1 ? 'font-size: 1.875rem; border-bottom: 1px solid var(--border);' : ''}
      ${this.level === 2 ? 'font-size: 1.5rem;' : ''}
      ${this.level === 3 ? 'font-size: 1.25rem;' : ''}
      ${this.level === 4 ? 'font-size: 1.125rem;' : ''}
      ${this.level === 5 ? 'font-size: 1rem;' : ''}
      ${this.level === 6 ? 'font-size: 0.875rem;' : ''}
    `
    return element
  }

  eq(other: HeadingWidget) {
    return this.level === other.level && this.text === other.text
  }
}

class BoldWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('strong')
    element.textContent = this.text
    element.className = 'cm-hybrid-bold'
    element.style.fontWeight = '600'
    return element
  }

  eq(other: BoldWidget) {
    return this.text === other.text
  }
}

class ItalicWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('em')
    element.textContent = this.text
    element.className = 'cm-hybrid-italic'
    element.style.fontStyle = 'italic'
    return element
  }

  eq(other: ItalicWidget) {
    return this.text === other.text
  }
}

class CodeWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('code')
    element.textContent = this.text
    element.className = 'cm-hybrid-code'
    element.style.cssText = `
      background: var(--muted);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'JetBrains Mono', monospace;
      font-size: 0.875em;
    `
    return element
  }

  eq(other: CodeWidget) {
    return this.text === other.text
  }
}

function buildDecorations(view: EditorView): DecorationSet {
  const decorations: any[] = []
  const doc = view.state.doc
  const tree = syntaxTree(view.state)
  const cursor = view.state.selection.main

  // 段落缓存 Map，避免重复计算
  const paragraphCache = new Map<string, Decoration>()

  tree.iterate({
    enter: (node) => {
      const { from, to, type } = node
      
      // 跳过光标所在的行，保持源码编辑状态
      const cursorLine = doc.lineAt(cursor.head)
      if (from >= cursorLine.from && to <= cursorLine.to) {
        return
      }

      const text = doc.sliceString(from, to)
      const cacheKey = `${type.name}-${from}-${to}-${text}`
      
      if (paragraphCache.has(cacheKey)) {
        decorations.push(paragraphCache.get(cacheKey))
        return
      }

      let decoration: Decoration | null = null

      switch (type.name) {
        case 'ATXHeading1':
        case 'ATXHeading2':
        case 'ATXHeading3':
        case 'ATXHeading4':
        case 'ATXHeading5':
        case 'ATXHeading6': {
          const level = parseInt(type.name.slice(-1))
          const headingText = text.replace(/^#+\s*/, '').replace(/\s*#+$/, '')
          if (headingText.trim()) {
            decoration = Decoration.replace({
              widget: new HeadingWidget(level, headingText),
              block: true
            }).range(from, to)
          }
          break
        }

        case 'StrongEmphasis': {
          const boldText = text.replace(/^\*\*/, '').replace(/\*\*$/, '')
          if (boldText.trim()) {
            decoration = Decoration.replace({
              widget: new BoldWidget(boldText)
            }).range(from, to)
          }
          break
        }

        case 'Emphasis': {
          const italicText = text.replace(/^\*/, '').replace(/\*$/, '')
          if (italicText.trim()) {
            decoration = Decoration.replace({
              widget: new ItalicWidget(italicText)
            }).range(from, to)
          }
          break
        }

        case 'InlineCode': {
          const codeText = text.replace(/^`/, '').replace(/`$/, '')
          if (codeText.trim()) {
            decoration = Decoration.replace({
              widget: new CodeWidget(codeText)
            }).range(from, to)
          }
          break
        }
      }

      if (decoration) {
        paragraphCache.set(cacheKey, decoration)
        decorations.push(decoration)
      }
    }
  })

  return Decoration.set(decorations)
}

const hybridPlugin = ViewPlugin.fromClass(
  class {
    decorations: DecorationSet
    private updateTimer: NodeJS.Timeout | null = null

    constructor(view: EditorView) {
      this.decorations = buildDecorations(view)
    }

    update(update: ViewUpdate) {
      // 节流更新，避免频繁重新计算
      if (this.updateTimer) {
        clearTimeout(this.updateTimer)
      }

      this.updateTimer = setTimeout(() => {
        if (update.docChanged || update.selectionSet) {
          this.decorations = buildDecorations(update.view)
        }
      }, 100) // 100ms 节流
    }

    destroy() {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer)
      }
    }
  },
  {
    decorations: (v) => v.decorations
  }
)

export function HybridExtension(): Extension {
  return [
    hybridPlugin,
    EditorView.theme({
      '.cm-hybrid-heading': {
        display: 'block',
        width: '100%'
      },
      '.cm-hybrid-h1': {
        borderBottom: '1px solid var(--border)',
        paddingBottom: '8px',
        marginBottom: '16px'
      },
      '.cm-hybrid-bold': {
        fontWeight: '600'
      },
      '.cm-hybrid-italic': {
        fontStyle: 'italic'
      },
      '.cm-hybrid-code': {
        backgroundColor: 'var(--muted)',
        padding: '2px 4px',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '0.875em'
      }
    })
  ]
}
