import { Extension } from '@codemirror/state'
import { EditorView } from '@codemirror/view'
import { oneDark } from '@codemirror/theme-one-dark'

/**
 * 主题扩展
 * 提供亮色和暗色主题支持
 */

const lightTheme = EditorView.theme({
  '&': {
    color: 'hsl(222.2 84% 4.9%)',
    backgroundColor: 'hsl(0 0% 100%)'
  },
  '.cm-content': {
    caretColor: 'hsl(221.2 83.2% 53.3%)'
  },
  '.cm-focused': {
    outline: 'none'
  },
  '.cm-selectionBackground, ::selection': {
    backgroundColor: 'hsl(210 40% 96%)'
  },
  '.cm-searchMatch': {
    backgroundColor: 'hsl(48 100% 67%)',
    outline: '1px solid hsl(45 100% 51%)'
  },
  '.cm-searchMatch.cm-searchMatch-selected': {
    backgroundColor: 'hsl(45 100% 51%)'
  },
  '.cm-activeLine': {
    backgroundColor: 'hsl(210 40% 98%)'
  },
  '.cm-selectionMatch': {
    backgroundColor: 'hsl(210 40% 92%)'
  },
  '&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket': {
    backgroundColor: 'hsl(210 40% 90%)',
    outline: '1px solid hsl(214.3 31.8% 91.4%)'
  },
  '.cm-gutters': {
    backgroundColor: 'hsl(210 40% 98%)',
    color: 'hsl(215.4 16.3% 46.9%)',
    border: 'none'
  },
  '.cm-activeLineGutter': {
    backgroundColor: 'hsl(210 40% 96%)'
  },
  '.cm-foldPlaceholder': {
    backgroundColor: 'transparent',
    border: 'none',
    color: 'hsl(215.4 16.3% 46.9%)'
  },
  '.cm-tooltip': {
    border: '1px solid hsl(214.3 31.8% 91.4%)',
    backgroundColor: 'hsl(0 0% 100%)',
    color: 'hsl(222.2 84% 4.9%)'
  },
  '.cm-tooltip .cm-tooltip-arrow:before': {
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent'
  },
  '.cm-tooltip .cm-tooltip-arrow:after': {
    borderTopColor: 'hsl(0 0% 100%)',
    borderBottomColor: 'hsl(0 0% 100%)'
  }
}, { dark: false })

export function ThemeExtension(theme: 'light' | 'dark' = 'light'): Extension {
  return theme === 'dark' ? oneDark : lightTheme
}
