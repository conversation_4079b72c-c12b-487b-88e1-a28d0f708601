import { Extension } from '@codemirror/state'
import { markdown } from '@codemirror/lang-markdown'
import { EditorView } from '@codemirror/view'

/**
 * Markdown 语法扩展
 * 提供 Markdown 语法高亮和编辑支持
 */

export function MarkdownExtension(): Extension {
  return [
    markdown({
      base: {
        // 启用 GitHub Flavored Markdown
        extensions: ['strikethrough', 'table', 'tasklist']
      }
    }),
    EditorView.theme({
      '.cm-line': {
        lineHeight: '1.6'
      },
      '.tok-heading': {
        fontWeight: '600'
      },
      '.tok-heading1': {
        fontSize: '1.5em',
        color: 'var(--primary)'
      },
      '.tok-heading2': {
        fontSize: '1.3em',
        color: 'var(--primary)'
      },
      '.tok-heading3': {
        fontSize: '1.1em',
        color: 'var(--primary)'
      },
      '.tok-strong': {
        fontWeight: '600',
        color: 'var(--foreground)'
      },
      '.tok-emphasis': {
        fontStyle: 'italic',
        color: 'var(--muted-foreground)'
      },
      '.tok-monospace': {
        backgroundColor: 'var(--muted)',
        padding: '2px 4px',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '0.9em'
      },
      '.tok-link': {
        color: 'var(--primary)',
        textDecoration: 'underline'
      },
      '.tok-url': {
        color: 'var(--muted-foreground)'
      },
      '.tok-quote': {
        color: 'var(--muted-foreground)',
        fontStyle: 'italic'
      },
      '.tok-list': {
        color: 'var(--primary)'
      },
      '.tok-strikethrough': {
        textDecoration: 'line-through',
        color: 'var(--muted-foreground)'
      }
    })
  ]
}
