declare module 'markdown-it-table-of-contents' {
  import { PluginSimple } from 'markdown-it'
  
  interface TocOptions {
    includeLevel?: number[]
    containerClass?: string
    slugify?: (s: string) => string
    markerPattern?: RegExp
    listType?: 'ul' | 'ol'
    format?: (anchor: string, htmlAnchor: string) => string
    containerHeaderHtml?: string
    containerFooterHtml?: string
    transformLink?: (link: string) => string
  }
  
  const plugin: PluginSimple
  export = plugin
}

declare module 'markdown-it-anchor' {
  import { PluginWithOptions } from 'markdown-it'
  
  interface AnchorOptions {
    level?: number | number[]
    slugify?: (s: string) => string
    permalink?: boolean | ((slug: string, opts: AnchorOptions, state: any, idx: number) => void)
    renderPermalink?: (slug: string, opts: AnchorOptions, state: any, idx: number) => void
    permalinkClass?: string
    permalinkSymbol?: string
    permalinkBefore?: boolean
    permalinkHref?: (slug: string, state: any) => string
    permalinkAttrs?: (slug: string, state: any) => Record<string, any>
    permalinkSpace?: boolean
    permalinkSide?: 'left' | 'right'
    tabIndex?: boolean | number
  }
  
  const plugin: PluginWithOptions<AnchorOptions>
  export = plugin
}
