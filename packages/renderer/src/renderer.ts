import MarkdownIt from 'markdown-it'
import emoji from 'markdown-it-emoji'
import katex from 'markdown-it-katex'
import anchor from 'markdown-it-anchor'
import toc from 'markdown-it-table-of-contents'
import DOMPurify from 'dompurify'
import mermaid from 'mermaid'

import type { RendererConfig, RenderResult, TocItem, MermaidConfig, KatexConfig } from './types'

export class MarkdownRenderer {
  private md: MarkdownIt
  private config: Required<RendererConfig>

  constructor(config: RendererConfig = {}) {
    this.config = {
      katex: true,
      mermaid: true,
      emoji: true,
      toc: true,
      anchor: true,
      classPrefix: 'markdown-',
      useWorker: false,
      sanitize: true,
      ...config
    }

    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      breaks: false
    })

    this.setupPlugins()
    this.setupMermaid()
  }

  private setupPlugins() {
    // Emoji 支持
    if (this.config.emoji) {
      this.md.use(emoji)
    }

    // KaTeX 数学公式支持
    if (this.config.katex) {
      this.md.use(katex, {
        throwOnError: false,
        errorColor: '#cc0000'
      } as KatexConfig)
    }

    // 锚点链接支持
    if (this.config.anchor) {
      this.md.use(anchor, {
        permalink: anchor.permalink.headerLink(),
        permalinkBefore: true,
        permalinkSymbol: '#'
      })
    }

    // 目录生成支持
    if (this.config.toc) {
      this.md.use(toc, {
        includeLevel: [1, 2, 3, 4, 5, 6],
        containerClass: `${this.config.classPrefix}toc`,
        listType: 'ul'
      })
    }

    // 自定义渲染规则
    this.setupCustomRules()
  }

  private setupCustomRules() {
    // 代码块渲染
    const defaultCodeBlockRenderer = this.md.renderer.rules.code_block
    this.md.renderer.rules.code_block = (tokens, idx, options, env, renderer) => {
      const token = tokens[idx]
      const langName = token.info ? token.info.trim() : ''
      
      if (langName === 'mermaid' && this.config.mermaid) {
        return this.renderMermaid(token.content)
      }

      return defaultCodeBlockRenderer?.(tokens, idx, options, env, renderer) || ''
    }

    // 围栏代码块渲染
    const defaultFenceRenderer = this.md.renderer.rules.fence
    this.md.renderer.rules.fence = (tokens, idx, options, env, renderer) => {
      const token = tokens[idx]
      const langName = token.info ? token.info.trim() : ''
      
      if (langName === 'mermaid' && this.config.mermaid) {
        return this.renderMermaid(token.content)
      }

      const result = defaultFenceRenderer?.(tokens, idx, options, env, renderer) || ''
      
      // 添加复制按钮
      if (langName) {
        return `
          <div class="${this.config.classPrefix}code-block">
            <div class="${this.config.classPrefix}code-header">
              <span class="${this.config.classPrefix}code-lang">${langName}</span>
              <button class="${this.config.classPrefix}copy-btn" data-copy="${this.escapeHtml(token.content)}">
                复制
              </button>
            </div>
            ${result}
          </div>
        `
      }

      return result
    }
  }

  private setupMermaid() {
    if (this.config.mermaid) {
      mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        maxTextSize: 50000
      } as MermaidConfig)
    }
  }

  private renderMermaid(content: string): string {
    if (!this.config.mermaid) return ''

    const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    return `
      <div class="${this.config.classPrefix}mermaid" id="${id}">
        <pre style="display: none;">${this.escapeHtml(content)}</pre>
        <div class="${this.config.classPrefix}mermaid-loading">
          正在渲染图表...
        </div>
      </div>
      <script>
        (function() {
          const element = document.getElementById('${id}');
          const content = element.querySelector('pre').textContent;
          const container = element.querySelector('.${this.config.classPrefix}mermaid-loading');
          
          if (window.mermaid) {
            window.mermaid.render('${id}-svg', content).then(function(result) {
              container.innerHTML = result.svg;
            }).catch(function(error) {
              container.innerHTML = '<div class="error">图表渲染失败: ' + error.message + '</div>';
            });
          }
        })();
      </script>
    `
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  private extractToc(html: string): TocItem[] {
    const toc: TocItem[] = []
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')
    const headings = doc.querySelectorAll('h1, h2, h3, h4, h5, h6')

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1))
      const text = heading.textContent || ''
      const anchor = heading.id || `heading-${index}`
      
      toc.push({
        level,
        text,
        anchor,
        position: index
      })
    })

    return this.buildTocTree(toc)
  }

  private buildTocTree(flatToc: TocItem[]): TocItem[] {
    const tree: TocItem[] = []
    const stack: TocItem[] = []

    for (const item of flatToc) {
      // 找到合适的父级
      while (stack.length > 0 && stack[stack.length - 1].level >= item.level) {
        stack.pop()
      }

      if (stack.length === 0) {
        tree.push(item)
      } else {
        const parent = stack[stack.length - 1]
        if (!parent.children) {
          parent.children = []
        }
        parent.children.push(item)
      }

      stack.push(item)
    }

    return tree
  }

  public render(markdown: string): RenderResult {
    const startTime = performance.now()
    
    try {
      let html = this.md.render(markdown)
      
      // 安全清理
      if (this.config.sanitize) {
        html = DOMPurify.sanitize(html, {
          ALLOWED_TAGS: [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p', 'br', 'hr',
            'strong', 'em', 'u', 's', 'del',
            'a', 'img',
            'ul', 'ol', 'li',
            'blockquote',
            'pre', 'code',
            'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'div', 'span',
            'svg', 'g', 'path', 'rect', 'circle', 'text'
          ],
          ALLOWED_ATTR: [
            'href', 'src', 'alt', 'title', 'id', 'class',
            'width', 'height', 'style',
            'data-*'
          ]
        })
      }

      const toc = this.config.toc ? this.extractToc(html) : undefined
      const renderTime = performance.now() - startTime

      return {
        html,
        toc,
        renderTime,
        hasError: false
      }
    } catch (error) {
      const renderTime = performance.now() - startTime
      
      return {
        html: `<div class="error">渲染错误: ${error instanceof Error ? error.message : '未知错误'}</div>`,
        renderTime,
        hasError: true,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  public updateConfig(config: Partial<RendererConfig>) {
    this.config = { ...this.config, ...config }
    // 重新初始化插件
    this.md = new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      breaks: false
    })
    this.setupPlugins()
    this.setupMermaid()
  }
}
