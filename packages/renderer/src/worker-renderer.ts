/**
 * Web Worker 渲染器
 * 在 Worker 线程中进行 Markdown 渲染，避免阻塞主线程
 */

import { MarkdownRenderer } from './renderer'
import type { RendererConfig, RenderResult } from './types'

export function createWorkerRenderer(config: RendererConfig = {}) {
  // 检查 Worker 支持
  if (typeof Worker === 'undefined') {
    console.warn('Web Worker 不支持，回退到主线程渲染')
    return new MarkdownRenderer(config)
  }

  // 创建 Worker 渲染器
  return {
    render: async (markdown: string): Promise<RenderResult> => {
      return new Promise((resolve, reject) => {
        // 创建内联 Worker
        const workerCode = `
          // 在 Worker 中导入渲染器
          importScripts('/worker-renderer-bundle.js');
          
          self.onmessage = function(e) {
            const { markdown, config } = e.data;
            
            try {
              const renderer = new MarkdownRenderer(config);
              const result = renderer.render(markdown);
              self.postMessage({ success: true, result });
            } catch (error) {
              self.postMessage({ 
                success: false, 
                error: error.message 
              });
            }
          };
        `

        const blob = new Blob([workerCode], { type: 'application/javascript' })
        const worker = new Worker(URL.createObjectURL(blob))

        // 设置超时
        const timeout = setTimeout(() => {
          worker.terminate()
          reject(new Error('Worker 渲染超时'))
        }, 10000) // 10秒超时

        worker.onmessage = (e) => {
          clearTimeout(timeout)
          worker.terminate()
          
          const { success, result, error } = e.data
          if (success) {
            resolve(result)
          } else {
            reject(new Error(error))
          }
        }

        worker.onerror = (error) => {
          clearTimeout(timeout)
          worker.terminate()
          reject(error)
        }

        // 发送渲染任务
        worker.postMessage({ markdown, config })
      })
    },

    updateConfig: (newConfig: Partial<RendererConfig>) => {
      Object.assign(config, newConfig)
    }
  }
}
