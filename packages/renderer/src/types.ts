export interface RendererConfig {
  /** 是否启用 KaTeX 数学公式 */
  katex?: boolean
  /** 是否启用 Mermaid 图表 */
  mermaid?: boolean
  /** 是否启用 Emoji */
  emoji?: boolean
  /** 是否启用目录生成 */
  toc?: boolean
  /** 是否启用锚点链接 */
  anchor?: boolean
  /** 自定义 CSS 类名前缀 */
  classPrefix?: string
  /** 是否在 Worker 中渲染 */
  useWorker?: boolean
  /** 安全配置 */
  sanitize?: boolean
}

export interface RenderResult {
  /** 渲染后的 HTML */
  html: string
  /** 提取的目录 */
  toc?: TocItem[]
  /** 渲染耗时（毫秒） */
  renderTime: number
  /** 是否有错误 */
  hasError: boolean
  /** 错误信息 */
  error?: string
}

export interface TocItem {
  /** 标题级别 */
  level: number
  /** 标题文本 */
  text: string
  /** 锚点 ID */
  anchor: string
  /** 在文档中的位置 */
  position: number
  /** 子标题 */
  children?: TocItem[]
}

export interface MermaidConfig {
  /** 主题 */
  theme?: 'default' | 'dark' | 'forest' | 'neutral'
  /** 是否启用安全模式 */
  securityLevel?: 'strict' | 'loose' | 'antiscript' | 'sandbox'
  /** 最大文本长度 */
  maxTextSize?: number
}

export interface KatexConfig {
  /** 是否抛出解析错误 */
  throwOnError?: boolean
  /** 错误颜色 */
  errorColor?: string
  /** 宏定义 */
  macros?: Record<string, string>
  /** 是否启用严格模式 */
  strict?: boolean
}
