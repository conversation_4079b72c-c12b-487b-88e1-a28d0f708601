{"name": "@xiehu/ui", "version": "0.1.0", "description": "写乎 UI 组件库", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-switch": "^1.0.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "lucide-react": "^0.294.0"}, "devDependencies": {"typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}